package pdfgenerator

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/models" // Adjust module path

	"github.com/gomarkdown/markdown/parser" // RE-ADDED for Extensions
	"github.com/mandolyte/mdtopdf/v2"
)

// MDToPDFRenderer (struct definition remains the same)
type MDToPDFRenderer struct {
	TemplateName  string
	Theme         string
	Orientation   string
	PageSize      string
	OutputLogFile string
	Title         string
	Author        string
	WithFooter    bool
}

// NewMDToPDFRenderer creates a new renderer instance.
// CORRECTED RETURN TYPE HERE
func NewMDToPDFRenderer(templateName, theme, orientation, pageSize, outputLogFile, title, author string, withFooter bool) *MDToPDFRenderer {
	return &MDToPDFRenderer{ // This correctly returns a pointer to MDToPDFRenderer
		TemplateName:  templateName,
		Theme:         theme,
		Orientation:   orientation,
		PageSize:      pageSize,
		OutputLogFile: outputLogFile,
		Title:         title,
		Author:        author,
		WithFooter:    withFooter,
	}
}

// Render function (as it was in the previous correct version where FPD<PERSON> test worked, then re-enabled Process)
func (r *MDToPDFRenderer) Render(data models.ReportData) ([]byte, error) {
	log.Println("Starting Markdown generation...")
	// Use the super-simplified Markdown template for this test
	markdownString, err := GenerateMarkdownFromTemplate("assessment_report_simplified.md", data) // Ensure this uses minimal MD
	if err != nil {
		return nil, fmt.Errorf("failed to generate markdown from template: %w", err)
	}
	log.Printf("Markdown generated successfully (length: %d)\n", len(markdownString))
	// os.WriteFile("debug_generated_report.md", []byte(markdownString), 0644)

	tmpPdfFile, err := os.CreateTemp("", "report-render-*.pdf")
	if err != nil {
		return nil, fmt.Errorf("failed to create temporary PDF file: %w", err)
	}
	tmpPdfPath := tmpPdfFile.Name()
	tmpPdfFile.Close()
	defer os.Remove(tmpPdfPath)

	log.Println("Initializing mdtopdf.PdfRenderer with PdfRendererParams...")

	var themeParam mdtopdf.Theme
	var customThemeFilePath string
	switch r.Theme {
	case "dark":
		themeParam = mdtopdf.DARK
	case "light":
		themeParam = mdtopdf.LIGHT
	default:
		if _, errStat := os.Stat(r.Theme); errStat == nil {
			themeParam = mdtopdf.CUSTOM
			customThemeFilePath = r.Theme
		} else {
			themeParam = mdtopdf.LIGHT
		}
	}
	orientationNormalized := "P"
	if r.Orientation == "landscape" || r.Orientation == "L" || r.Orientation == "Landscape" {
		orientationNormalized = "L"
	}
	params := mdtopdf.PdfRendererParams{
		PdfFile: tmpPdfPath, TracerFile: r.OutputLogFile, Orientation: orientationNormalized,
		Papersz: r.PageSize, Theme: themeParam, CustomThemeFile: customThemeFilePath,
	}
	pdfRenderer := mdtopdf.NewPdfRenderer(params)
	if pdfRenderer == nil || pdfRenderer.Pdf == nil {
		return nil, fmt.Errorf("NewPdfRenderer returned nil or uninitialized Fpdf object")
	}
	log.Printf("mdtopdf.PdfRenderer initialized. Theme const: %v", params.Theme)

	// Direct FPDF write test (kept for now)
	pdfRenderer.Pdf.SetFont("Arial", "", 12)
	pdfRenderer.Pdf.SetTextColor(0, 0, 0)
	pdfRenderer.Pdf.SetXY(20, 30)
	pdfRenderer.Pdf.Cell(40, 10, "Direct Pre-Test")
	if pdfRenderer.Pdf.Error() != nil {
		log.Printf("ERROR during direct FPDF Pre-Test Cell call: %s", pdfRenderer.Pdf.Error().Error())
	} else {
		log.Println("DEBUG: Direct FPDF Pre-Test Cell call OK.")
	}

	pdfRenderer.Extensions = parser.CommonExtensions | parser.Tables | parser.AutoHeadingIDs | parser.Attributes
	log.Println("Markdown parser extensions enabled.")

	if r.Title != "" {
		pdfRenderer.Pdf.SetTitle(r.Title, true)
	}
	if r.Author != "" {
		pdfRenderer.Pdf.SetAuthor(r.Author, true)
	}
	if r.WithFooter {
		pdfRenderer.Pdf.SetFooterFunc(func() {
			pdfRenderer.Pdf.SetY(-15)
			pdfRenderer.Pdf.SetFont("Arial", "I", 8)
			footerContent := fmt.Sprintf("Page %d/{nb}", pdfRenderer.Pdf.PageNo())
			pdfRenderer.Pdf.CellFormat(0, 10, footerContent, "", 0, "C", false, 0, "")
		})
	}

	log.Println("Processing Markdown with mdtopdf...")
	markdownBytes := []byte(markdownString)
	err = pdfRenderer.Process(markdownBytes)
	if err != nil {
		return nil, fmt.Errorf("mdtopdf library failed to process markdown: %w", err)
	}
	log.Println("Markdown processed by mdtopdf successfully.")

	err = pdfRenderer.Pdf.OutputFileAndClose(tmpPdfPath)
	if err != nil {
		return nil, fmt.Errorf("fpdf OutputFileAndClose failed for %s: %w", tmpPdfPath, err)
	}

	pdfFileBytes, err := os.ReadFile(tmpPdfPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read temporary PDF file %s: %w", tmpPdfPath, err)
	}
	log.Printf("PDF generated (size: %d bytes)", len(pdfFileBytes))

	return pdfFileBytes, nil
}
