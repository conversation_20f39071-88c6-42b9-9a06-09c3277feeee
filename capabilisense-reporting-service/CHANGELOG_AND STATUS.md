# Changelog & Status: CapabiliSense PDF Reporting Service (Go)

## Current Version: v0.0.1 (Initial Development)

## Date: 2025-05-23

## Status:

*   **Overall:** In initial Go development phase for a placeholder PDF generation capability.
*   **Stage A (Data Extraction - Go):** Not yet implemented. Mock data is currently used.
*   **Stage B (AI Insights - Go):** Not yet implemented. Mock data is currently used. LLM client library and prompt integration are pending.
*   **Stage C (PDF Generation - Go):**
    *   **Templating:** `text/template` for Markdown generation is set up (`template_manager.go`).
    *   **PDF Library (`mdtopdf`):**
        *   Successfully integrated `github.com/mandolyte/mdtopdf/v2` to be called programmatically.
        *   **CURRENT BLOCKER:** `mdtopdf` produces a 0-byte PDF even for very simple ASCII Markdown and default "light" theme settings.
        *   Direct `fpdf` calls (bypassing `mdtopdf`'s Markdown processing) *do* produce a valid PDF with content. This indicates the issue lies within `mdtopdf`'s rendering of parsed Markdown nodes or its default style application.
        *   The `mdtopdf` tracer log shows correct parsing of simple Markdown into AST nodes (Headings, Paragraphs, Text).
        *   Table parsing was enabled but also resulted in 0-byte PDF, likely due to similar underlying rendering issues in `mdtopdf` for table elements or its default table styles.
    *   **Current PDF Output:** 0-byte PDF.
*   **Service Interface:** Basic HTTP server setup in `main.go` to serve the (currently 0-byte) PDF.

## Decisions Made:

*   **Initial PDF Generation Strategy:** Use `mdtopdf` with a simplified, table-less, ASCII-only Markdown template as a placeholder to get an end-to-end flow working.
*   **Pausing `mdtopdf` Deep Debugging:** Due to persistent 0-byte output with `mdtopdf` even for simple cases, further deep debugging of this library will be paused.
*   **Next Step for PDF Generation:** If `mdtopdf` cannot be made to work quickly for basic ASCII text, the next attempt for Stage C will be to switch to an HTML-based templating approach and use a headless browser library in Go (e.g., `chromedp`) to convert the generated HTML to PDF. This is expected to offer better rendering fidelity and font/Unicode handling.
*   **AI LLM Library:** To be developed in Go, supporting `prompts_library.json` features (model selection, structured output, fallbacks).
*   **Data Flow:** Stages A (Data) -> B (AI Insights) -> C (Templating + PDF Rendering).
*   **Organization Name:** To be dynamically extracted by an AI prompt in Stage B.

## Action Items:

*   **Verify minimal text rendering with `mdtopdf`:** One final attempt with the absolute simplest setup for `mdtopdf` (e.g., minimal `PdfRendererParams`, explicit basic font set on `fpdf.Fpdf` object *before* `Process` if `mdtopdf` doesn't do it reliably).
*   **If `mdtopdf` still fails for basic text:** Pivot Stage C's `Renderer` implementation to use HTML templating and `chromedp` (or similar headless browser Go library).
*   Begin implementation of Stage A (Data Extraction) in Go, defining Go structs for `AnalysisResults` and `FrameworkContent`.
*   Begin design and implementation of the Go `prompts_library.go` and `llmclient.go`.